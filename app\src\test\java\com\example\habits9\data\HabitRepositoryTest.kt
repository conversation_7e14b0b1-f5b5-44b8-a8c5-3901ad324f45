package com.example.habits9.data

import com.example.habits9.data.firestore.FirestoreConverters
import org.junit.Test
import org.junit.Assert.*

/**
 * Test class to verify that habit serialization works correctly for Firestore.
 * This specifically tests the fix for the daysOfWeek serialization issue.
 */
class HabitRepositoryTest {

    @Test
    fun testHabitWithWeeklyFrequencySerialization() {
        // Create a habit with weekly frequency and specific days
        val habit = Habit(
            name = "Test Habit",
            description = "Test habit with weekly frequency",
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = listOf(1L, 4L, 6L) // Monday, Thursday, Saturday
        )

        // Convert to Firestore format
        val firestoreHabit = FirestoreConverters.habitToFirestore(habit)

        // Verify that daysOfWeek is correctly preserved as a list
        assertEquals(listOf(1L, 4L, 6L), firestoreHabit.daysOfWeek)
        assertEquals("WEEKLY", firestoreHabit.frequencyType)
        assertEquals(1, firestoreHabit.repeatsEvery)
    }

    @Test
    fun testHabitWithEmptyDaysOfWeek() {
        // Create a habit with empty daysOfWeek (daily habit)
        val habit = Habit(
            name = "Daily Habit",
            description = "Test daily habit",
            frequencyType = "DAILY",
            repeatsEvery = 1,
            daysOfWeek = emptyList()
        )

        // Convert to Firestore format
        val firestoreHabit = FirestoreConverters.habitToFirestore(habit)

        // Verify that empty list is preserved
        assertEquals(emptyList<Long>(), firestoreHabit.daysOfWeek)
        assertEquals("DAILY", firestoreHabit.frequencyType)
    }

    @Test
    fun testHabitWithWeeklyEmptyDaysOfWeek() {
        // Create a habit with empty daysOfWeek list
        val habit = Habit(
            name = "Test Habit",
            description = "Test habit with empty days",
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = emptyList()
        )

        // Convert to Firestore format
        val firestoreHabit = FirestoreConverters.habitToFirestore(habit)

        // Verify that empty list is preserved
        assertEquals(emptyList<Long>(), firestoreHabit.daysOfWeek)
    }

    @Test
    fun testHabitDataMapCreation() {
        // Test the Map creation logic that we use in HabitRepository
        val habit = Habit(
            name = "Map Test Habit",
            description = "Testing map creation",
            frequencyType = "WEEKLY",
            repeatsEvery = 2,
            daysOfWeek = listOf(2L, 5L, 7L) // Tuesday, Friday, Sunday
        )

        // Create the same map structure as in HabitRepository.insertHabit
        val habitData = mapOf(
            "id" to if (habit.id == 0L) "" else habit.id.toString(),
            "name" to habit.name,
            "description" to habit.description,
            "creationDate" to habit.creationDate,
            "currentStreak" to habit.currentStreak,
            "completionDatesJson" to habit.completionDatesJson,
            "uuid" to habit.uuid,
            "isArchived" to habit.isArchived,
            "position" to habit.position,
            "color" to habit.color,
            "type" to habit.type,
            "targetType" to habit.targetType,
            "targetValue" to habit.targetValue,
            "unit" to habit.unit,
            "frequencyType" to habit.frequencyType,
            "repeatsEvery" to habit.repeatsEvery,
            "daysOfWeek" to habit.daysOfWeek,
            "dayOfMonth" to habit.dayOfMonth,
            "weekOfMonth" to habit.weekOfMonth,
            "dayOfWeekInMonth" to habit.dayOfWeekInMonth
        )

        // Verify the map contains the correct values
        assertEquals("Map Test Habit", habitData["name"])
        assertEquals("WEEKLY", habitData["frequencyType"])
        assertEquals(2, habitData["repeatsEvery"])
        assertEquals("2,5,7", habitData["daysOfWeek"])
        
        // Verify that daysOfWeek in the map is a String, not an array
        assertTrue("daysOfWeek should be a String", habitData["daysOfWeek"] is String)
    }
}
