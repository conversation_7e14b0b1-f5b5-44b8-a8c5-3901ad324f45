package com.example.habits9.data

import android.util.Log
import com.example.habits9.data.firestore.FirestoreConverters
import com.example.habits9.data.firestore.FirestoreHabit
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.ListenerRegistration
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class HabitRepository @Inject constructor(
    private val firestore: FirebaseFirestore,
    private val auth: FirebaseAuth
) {

    companion object {
        private const val TAG = "HabitRepository"
        private const val USERS_COLLECTION = "users"
        private const val HABITS_COLLECTION = "habits"
    }

    /**
     * Gets all habits for the current user using real-time listeners.
     * Returns an empty flow if user is not authenticated.
     */
    fun getAllHabits(): Flow<List<Habit>> = callbackFlow {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, returning empty habits list")
            trySend(emptyList())
            close()
            return@callbackFlow
        }

        val listener: ListenerRegistration = firestore
            .collection(USERS_COLLECTION)
            .document(userId)
            .collection(HABITS_COLLECTION)
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    Log.e(TAG, "Error listening to habits", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null) {
                    val habits = snapshot.documents.mapNotNull { document ->
                        try {
                            val firestoreHabit = document.toObject(FirestoreHabit::class.java)
                            firestoreHabit?.let {
                                // Use document ID as the Firestore ID
                                val habitWithId = it.copy(id = document.id)
                                FirestoreConverters.firestoreToHabit(habitWithId)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error converting document to habit: ${document.id}", e)
                            null
                        }
                    }
                    trySend(habits)
                } else {
                    trySend(emptyList())
                }
            }

        awaitClose { listener.remove() }
    }

    /**
     * Gets a specific habit by ID using real-time listener.
     */
    fun getHabitById(habitId: Long): Flow<Habit> = callbackFlow {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated")
            close(IllegalStateException("User not authenticated"))
            return@callbackFlow
        }

        val listener: ListenerRegistration = firestore
            .collection(USERS_COLLECTION)
            .document(userId)
            .collection(HABITS_COLLECTION)
            .document(habitId.toString())
            .addSnapshotListener { snapshot, error ->
                if (error != null) {
                    Log.e(TAG, "Error listening to habit $habitId", error)
                    close(error)
                    return@addSnapshotListener
                }

                if (snapshot != null && snapshot.exists()) {
                    try {
                        val firestoreHabit = snapshot.toObject(FirestoreHabit::class.java)
                        firestoreHabit?.let {
                            val habitWithId = it.copy(id = snapshot.id)
                            val habit = FirestoreConverters.firestoreToHabit(habitWithId)
                            trySend(habit)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error converting habit document", e)
                        close(e)
                    }
                } else {
                    close(NoSuchElementException("Habit not found"))
                }
            }

        awaitClose { listener.remove() }
    }

    /**
     * Gets a specific habit by ID synchronously (one-time read).
     */
    suspend fun getHabitByIdSync(habitId: Long): Habit? {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated")
            return null
        }

        return try {
            val snapshot = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .document(habitId.toString())
                .get()
                .await()

            if (snapshot.exists()) {
                val firestoreHabit = snapshot.toObject(FirestoreHabit::class.java)
                firestoreHabit?.let {
                    val habitWithId = it.copy(id = snapshot.id)
                    FirestoreConverters.firestoreToHabit(habitWithId)
                }
            } else {
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error getting habit $habitId", e)
            null
        }
    }

    /**
     * Inserts a new habit for the current user.
     */
    suspend fun insertHabit(habit: Habit) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot insert habit")
            throw IllegalStateException("User not authenticated")
        }

        try {
            // Convert habit to a Map to ensure proper Firestore serialization
            // This prevents the daysOfWeek from being serialized as a raw array string
            val habitData = mapOf(
                "id" to if (habit.id == 0L) "" else habit.id.toString(),
                "name" to habit.name,
                "description" to habit.description,
                "creationDate" to habit.creationDate,
                "currentStreak" to habit.currentStreak,
                "completionDatesJson" to habit.completionDatesJson,
                "uuid" to habit.uuid,
                "isArchived" to habit.isArchived,
                "position" to habit.position,
                "color" to habit.color,
                "type" to habit.type,
                "targetType" to habit.targetType,
                "targetValue" to habit.targetValue,
                "unit" to habit.unit,
                "frequencyType" to habit.frequencyType,
                "repeatsEvery" to habit.repeatsEvery,
                "daysOfWeek" to habit.daysOfWeek, // This stores the list of day numbers
                "dayOfMonth" to habit.dayOfMonth,
                "weekOfMonth" to habit.weekOfMonth,
                "dayOfWeekInMonth" to habit.dayOfWeekInMonth
            )

            // Add the habit to Firestore (auto-generate document ID)
            val documentRef = firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .add(habitData)
                .await()

            Log.d(TAG, "Habit inserted with ID: ${documentRef.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error inserting habit", e)
            throw e
        }
    }

    /**
     * Updates an existing habit for the current user.
     */
    suspend fun updateHabit(habit: Habit) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot update habit")
            throw IllegalStateException("User not authenticated")
        }

        try {
            // Convert habit to a Map to ensure proper Firestore serialization
            val habitData = mapOf(
                "id" to if (habit.id == 0L) "" else habit.id.toString(),
                "name" to habit.name,
                "description" to habit.description,
                "creationDate" to habit.creationDate,
                "currentStreak" to habit.currentStreak,
                "completionDatesJson" to habit.completionDatesJson,
                "uuid" to habit.uuid,
                "isArchived" to habit.isArchived,
                "position" to habit.position,
                "color" to habit.color,
                "type" to habit.type,
                "targetType" to habit.targetType,
                "targetValue" to habit.targetValue,
                "unit" to habit.unit,
                "frequencyType" to habit.frequencyType,
                "repeatsEvery" to habit.repeatsEvery,
                "daysOfWeek" to habit.daysOfWeek, // This stores the list of day numbers
                "dayOfMonth" to habit.dayOfMonth,
                "weekOfMonth" to habit.weekOfMonth,
                "dayOfWeekInMonth" to habit.dayOfWeekInMonth
            )

            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .document(habit.id.toString())
                .set(habitData)
                .await()

            Log.d(TAG, "Habit updated: ${habit.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error updating habit ${habit.id}", e)
            throw e
        }
    }

    /**
     * Deletes a habit for the current user.
     */
    suspend fun deleteHabit(habit: Habit) {
        val userId = auth.currentUser?.uid
        if (userId == null) {
            Log.w(TAG, "User not authenticated, cannot delete habit")
            throw IllegalStateException("User not authenticated")
        }

        try {
            firestore
                .collection(USERS_COLLECTION)
                .document(userId)
                .collection(HABITS_COLLECTION)
                .document(habit.id.toString())
                .delete()
                .await()

            Log.d(TAG, "Habit deleted: ${habit.id}")
        } catch (e: Exception) {
            Log.e(TAG, "Error deleting habit ${habit.id}", e)
            throw e
        }
    }
}
