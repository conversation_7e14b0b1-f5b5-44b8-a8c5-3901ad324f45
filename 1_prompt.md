# 🐞 Prompt: Fix Critical Home Screen Interaction Bugs

## 1. Objective

To fix two critical bugs on the home screen that prevent correct habit tracking: 1) Marking one "Yes/No" habit completes all habits for that day and cannot be undone. 2) "Measurable" habits are unresponsive and do not open the input dialog when tapped.

## 2. Part 1: Fix "Yes/No" Habit Bugs

### 2.1 Visual Reference

The bug is clearly demonstrated in **`44.jpg`**. As shown, marking "Hab3" as complete for Wednesday the 23rd has incorrectly marked all other habits for that day as complete.

### 2.2 Root Cause Analysis

There are two distinct issues with "Yes/No" habits:

1.  **Incorrect Mass Completion:** The logic that creates a completion record is likely only using the `date` of the tap and is not correctly identifying the specific **`habitId`** that was tapped. This results in a generic completion event that the UI misinterprets for all habits on that day.
2.  **Inability to Unmark:** The current logic only handles adding a completion record. It does not have a path to **delete** the completion record if the user taps on an already completed habit.

### 2.3 Implementation Plan

* **Task 1: Isolate Habit Completion**
    * In the `HomeScreen.kt` Composable, locate the `onClick` handler for the daily completion cells.
    * Ensure this handler passes the specific **`habitId`** of the tapped habit, along with the `date`, to the `ViewModel`.
    * In the `ViewModel` and `CompletionRepository`, update the `addCompletion` function (or equivalent) to create a `Completion` document in Firestore that is explicitly tied to both the `userId` and the specific `habitId`.

* **Task 2: Implement Completion Toggle Logic**
    * In the `ViewModel`, refactor the function that handles the completion tap.
    * This function must now first **check if a completion record already exists** for the given `habitId` and `date`.
    * **If a completion exists:** Call a new `repository.deleteCompletion(habitId, date)` function, which must delete the corresponding document from Firestore.
    * **If no completion exists:** Call the existing `repository.addCompletion(habitId, date)` function to create the document.

---

## 3. Part 2: Fix Unresponsive "Measurable" Habits

### 3.1 Root Cause Analysis

The `onClick` handler on the `HomeScreen` is not differentiating between habit types. It likely has a single action that only attempts to toggle a "Yes/No" completion, which has no effect on a "Measurable" habit.

### 3.2 Implementation Plan

* **Task 1: Differentiate Click Behavior Based on Habit Type**
    * In the main `onClick` handler within `HomeScreen.kt`, you must get the `type` of the habit that was clicked.
    * Use a `when` statement or an `if/else` block to create two distinct code paths:
        * If `habit.typeEnum == HabitType.YES_NO`, execute the completion toggle logic described in Part 1.
        * If `habit.typeEnum == HabitType.MEASURABLE`, the app must trigger a UI event to show the **`NumericalInputDialog`**. This dialog needs to be passed the `habitId` and `date` so it can save the user's input to the correct completion record in Firestore.

---

## 4. Verification / Testing Section

* **Test Case 1 (Yes/No Isolation):**
    * With multiple "Yes/No" habits on the screen, tap to complete **one** of them.
    * **Expected Outcome:** Only the tapped habit is marked as complete. All other habits remain unchanged.

* **Test Case 2 (Yes/No Toggle):**
    * Tap a completed "Yes/No" habit again.
    * **Expected Outcome:** The habit's status must toggle back to incomplete. Check the Firestore console to confirm the completion document is created on the first tap and deleted on the second.

* **Test Case 3 (Measurable Habit Dialog):**
    * Tap on a "Measurable" habit's cell.
    * **Expected Outcome:** The numerical input dialog must appear, asking for a value.

* **Test Case 4 (Measurable Habit Data Entry):**
    * Enter a number in the dialog and save it.
    * **Expected Outcome:** The dialog should close, the cell on the home screen should update to show the entered value (e.g., "5/10 km"), and a `Completion` document should be created in Firestore containing the correct `value`.

## 5. Mandatory Development Guidelines

**These practices must be followed during all phases of development—planning, implementation, and review.**

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.

# Please Follow the below debuging guidelines while resolving the error 


# Debug Process Guide

Please follow this exact structured debugging process to investigate and resolve the issue:


## 1. Understand the Error

- thoroughly understand the error context!
- Carefully interpret what the error says:
  - Understand the type of exception
  - Analyze the stack trace
  - Note any file paths or line numbers referenced

## 2. Trace the Error Location

- Navigate to all relevant files and lines mentioned in the error log
- Understand the surrounding code and the full implementation of the feature where the error occurred
- Focus on:
  - Control flow
  - Input/output
  - Any dependent components

## 3. Comprehend the Current Feature Implementation

- Develop a clear mental model of how the entire feature is structured and functions
- Identify how the involved files and methods interact
- Understand what they're intended to do

## 4. Determine the Root Cause

> **Important**: Before implementing any changes, it is mandatory to identify and finalize the true root cause of the issue.

Think deeply about potential causes:
- Logic error
- Missing configuration
- Incompatible encoding
- Race condition
- Misused library

**Clearly state the root cause once identified.**

## 5. Cross-Reference the Reference Project

- Once the root cause is finalized, examine the reference project at `./uhabits-dev`
- Compare relevant parts of the implementation
- Look for differences or proven approaches that could guide the solution

## 6. Plan and Execute the Fix

After gaining full understanding and validating it against the reference project, proceed to implement the fix with precision.

Ensure that:
1. The change is minimal and localized
2. It addresses the root cause directly
3. It does not introduce side effects

## 7. Verify

Test the fix thoroughly to ensure the issue is resolved.
