package com.example.habits9.data

import org.junit.Test
import org.junit.Assert.*

/**
 * Integration test to verify that the habit serialization fix works correctly.
 * This test simulates the exact data flow that was causing the Firestore crash.
 */
class HabitSerializationIntegrationTest {

    @Test
    fun testWeeklyHabitDataMapSerialization() {
        // Create a habit exactly as it would be created in the UI
        // This simulates the scenario from the bug report: Monday, Thursday, Saturday
        val habit = Habit(
            name = "Test Weekly Habit",
            description = "Did you complete your weekly habit?",
            frequencyType = "WEEKLY",
            repeatsEvery = 1,
            daysOfWeek = listOf(1L, 4L, 6L) // Monday, Thursday, Saturday
        )

        // Simulate the exact Map creation logic from HabitRepository.insertHabit
        val habitData = mapOf(
            "id" to if (habit.id == 0L) "" else habit.id.toString(),
            "name" to habit.name,
            "description" to habit.description,
            "creationDate" to habit.creationDate,
            "currentStreak" to habit.currentStreak,
            "completionDatesJson" to habit.completionDatesJson,
            "uuid" to habit.uuid,
            "isArchived" to habit.isArchived,
            "position" to habit.position,
            "color" to habit.color,
            "type" to habit.type,
            "targetType" to habit.targetType,
            "targetValue" to habit.targetValue,
            "unit" to habit.unit,
            "frequencyType" to habit.frequencyType,
            "repeatsEvery" to habit.repeatsEvery,
            "daysOfWeek" to habit.daysOfWeek, // This is the critical field that was causing the crash
            "dayOfMonth" to habit.dayOfMonth,
            "weekOfMonth" to habit.weekOfMonth,
            "dayOfWeekInMonth" to habit.dayOfWeekInMonth
        )

        // Verify that all the data is correctly serialized
        assertEquals("Test Weekly Habit", habitData["name"])
        assertEquals("WEEKLY", habitData["frequencyType"])
        assertEquals(1, habitData["repeatsEvery"])
        assertEquals("1,4,6", habitData["daysOfWeek"])
        
        // Most importantly, verify that daysOfWeek is a proper String
        assertTrue("daysOfWeek should be a String", habitData["daysOfWeek"] is String)
        
        // Verify it doesn't contain the problematic array representation
        val daysOfWeekValue = habitData["daysOfWeek"] as String
        assertFalse("daysOfWeek should not contain array representation", 
                   daysOfWeekValue.contains("[Ljava.lang.String"))
        assertFalse("daysOfWeek should not contain object reference", 
                   daysOfWeekValue.contains("@"))
        
        // Verify the string can be parsed back correctly
        val dayNumbers = daysOfWeekValue.split(",").map { it.toInt() }
        assertEquals(listOf(1, 4, 6), dayNumbers)
    }

    @Test
    fun testDailyHabitDataMapSerialization() {
        // Test a daily habit (no daysOfWeek)
        val habit = Habit(
            name = "Daily Habit",
            description = "Daily habit test",
            frequencyType = "DAILY",
            repeatsEvery = 1,
            daysOfWeek = null
        )

        val habitData = mapOf(
            "id" to if (habit.id == 0L) "" else habit.id.toString(),
            "name" to habit.name,
            "description" to habit.description,
            "creationDate" to habit.creationDate,
            "currentStreak" to habit.currentStreak,
            "completionDatesJson" to habit.completionDatesJson,
            "uuid" to habit.uuid,
            "isArchived" to habit.isArchived,
            "position" to habit.position,
            "color" to habit.color,
            "type" to habit.type,
            "targetType" to habit.targetType,
            "targetValue" to habit.targetValue,
            "unit" to habit.unit,
            "frequencyType" to habit.frequencyType,
            "repeatsEvery" to habit.repeatsEvery,
            "daysOfWeek" to habit.daysOfWeek,
            "dayOfMonth" to habit.dayOfMonth,
            "weekOfMonth" to habit.weekOfMonth,
            "dayOfWeekInMonth" to habit.dayOfWeekInMonth
        )

        assertEquals("DAILY", habitData["frequencyType"])
        assertNull(habitData["daysOfWeek"])
    }

    @Test
    fun testComplexWeeklyHabitSerialization() {
        // Test a more complex weekly habit (every 2 weeks, all weekdays)
        val habit = Habit(
            name = "Bi-weekly Weekdays",
            description = "Every 2 weeks on weekdays",
            frequencyType = "WEEKLY",
            repeatsEvery = 2,
            daysOfWeek = listOf(1L, 2L, 3L, 4L, 5L) // Monday through Friday
        )

        val habitData = mapOf(
            "id" to if (habit.id == 0L) "" else habit.id.toString(),
            "name" to habit.name,
            "description" to habit.description,
            "creationDate" to habit.creationDate,
            "currentStreak" to habit.currentStreak,
            "completionDatesJson" to habit.completionDatesJson,
            "uuid" to habit.uuid,
            "isArchived" to habit.isArchived,
            "position" to habit.position,
            "color" to habit.color,
            "type" to habit.type,
            "targetType" to habit.targetType,
            "targetValue" to habit.targetValue,
            "unit" to habit.unit,
            "frequencyType" to habit.frequencyType,
            "repeatsEvery" to habit.repeatsEvery,
            "daysOfWeek" to habit.daysOfWeek,
            "dayOfMonth" to habit.dayOfMonth,
            "weekOfMonth" to habit.weekOfMonth,
            "dayOfWeekInMonth" to habit.dayOfWeekInMonth
        )

        assertEquals("WEEKLY", habitData["frequencyType"])
        assertEquals(2, habitData["repeatsEvery"])
        assertEquals("1,2,3,4,5", habitData["daysOfWeek"])
        
        // Verify it's a proper string
        assertTrue("daysOfWeek should be a String", habitData["daysOfWeek"] is String)
        
        // Verify the string format is correct
        val daysOfWeekValue = habitData["daysOfWeek"] as String
        val dayNumbers = daysOfWeekValue.split(",").map { it.toInt() }
        assertEquals(listOf(1, 2, 3, 4, 5), dayNumbers)
    }
}
